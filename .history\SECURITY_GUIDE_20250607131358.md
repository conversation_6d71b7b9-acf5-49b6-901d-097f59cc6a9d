# Security Guide for IIT Palakkad Website

## Current Security Status

### ✅ What's Protected Now
- **Admin Interface**: Password-protected access to `/admin`
- **Media Upload API**: Requires authentication to upload/delete files
- **Session Management**: Simple cookie-based authentication

### ⚠️ What's Still Public
- **Uploaded Media Files**: Files in `/uploads/` are publicly accessible
- **Website Content**: All public pages are accessible to everyone
- **Media URLs**: Anyone with the direct URL can view uploaded files

## Security Levels Explained

### Level 1: Current Setup (Basic Protection)
```
✅ Admin panel protected
✅ Upload API protected  
❌ Media files public
❌ No user management
```

### Level 2: Enhanced Security (Recommended)
```
✅ Admin panel protected
✅ Upload API protected
✅ Media files protected
✅ Role-based access
✅ Secure file serving
```

### Level 3: Enterprise Security
```
✅ All Level 2 features
✅ Database authentication
✅ JWT tokens
✅ Rate limiting
✅ Audit logging
```

## Quick Setup Instructions

### 1. Set Admin Password
Create a `.env.local` file in your project root:
```bash
ADMIN_PASSWORD=YourSecurePassword123!
```

### 2. Access Admin Panel
1. Go to `yourdomain.com/admin`
2. You'll be redirected to login page
3. Enter your password
4. Access granted for 24 hours

### 3. Upload Media
1. Login to admin panel
2. Go to Media tab
3. Upload your files
4. Copy URLs to use in other sections

## Media File Visibility Options

### Option A: Keep Files Public (Current)
**Pros:**
- Simple setup
- Fast loading
- Works with CDNs
- No server processing needed

**Cons:**
- Anyone can access files with direct URLs
- No access control
- Files visible to search engines

**Use Case:** Public website content, logos, public images

### Option B: Protect Media Files
If you want to protect uploaded files, here's how:

1. **Move uploads outside public directory**
2. **Create protected file serving API**
3. **Add authentication checks**

Would you like me to implement protected file serving?

## Hosting Considerations

### Public Hosting (Vercel, Netlify, etc.)
```bash
# Your files will be accessible at:
https://yoursite.com/uploads/images/photo.jpg

# Anyone in the world can access this URL
```

### Private Hosting Options
1. **Password-protect entire site**
2. **Use private repositories**
3. **Implement user authentication**
4. **Use cloud storage with access controls**

## Recommended Security Measures

### For Public Websites
1. ✅ Keep current setup
2. ✅ Use strong admin password
3. ✅ Regular backups
4. ✅ Monitor access logs
5. ✅ Update dependencies

### For Private/Internal Websites
1. ✅ Implement protected file serving
2. ✅ Add user authentication
3. ✅ Use HTTPS only
4. ✅ Implement rate limiting
5. ✅ Add audit logging

## Implementation Examples

### Protect Specific File Types
```typescript
// Only protect sensitive documents
if (request.nextUrl.pathname.includes('/uploads/documents/')) {
  // Require authentication
}
// Keep images public
```

### Role-Based Access
```typescript
// Different access levels
const userRole = getUserRole(request)
if (userRole === 'admin') {
  // Full access
} else if (userRole === 'editor') {
  // Limited access
} else {
  // Public access only
}
```

## Quick Security Checklist

### Before Going Live
- [ ] Set strong admin password in `.env.local`
- [ ] Test admin login functionality
- [ ] Verify file upload works
- [ ] Check media URLs are accessible
- [ ] Test logout functionality
- [ ] Review what should be public vs private

### After Going Live
- [ ] Monitor admin access logs
- [ ] Regular password updates
- [ ] Backup uploaded files
- [ ] Monitor file storage usage
- [ ] Review and clean unused files

## FAQ

### Q: Will my uploaded photos be visible to everyone?
**A:** Yes, currently uploaded files are publicly accessible if someone knows the URL. The admin interface is protected, but the files themselves are public.

### Q: How do I make files private?
**A:** You would need to implement protected file serving (I can help with this). Files would be moved outside the public directory and served through an authenticated API.

### Q: Is this secure enough for a university website?
**A:** For public content (research photos, publications, etc.), yes. For sensitive documents, consider implementing protected file serving.

### Q: Can I password-protect the entire website?
**A:** Yes, you can add authentication to all routes, not just admin. This would make the entire site private.

### Q: What happens if someone guesses my file URLs?
**A:** They can access the files directly. Use protected file serving if this is a concern.

## Need More Security?

If you need enhanced security features like:
- Protected file serving
- User management
- Role-based access
- Database authentication
- Audit logging

Let me know and I can implement these features for you!

## Contact for Security Questions

If you have specific security requirements or concerns, please provide details about:
1. What type of content you're hosting
2. Who should have access
3. Your security requirements
4. Compliance needs (if any)

This will help determine the best security approach for your use case.
