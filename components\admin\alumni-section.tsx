"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Edit, Trash2, Save, X, GraduationCap, Building, MapPin, ImageIcon } from "lucide-react"
import Image from "next/image"
import { useData } from "@/lib/data-context"
import MediaBrowser from "./media-browser"

export default function AlumniSection() {
  const { alumni, add<PERSON>lum<PERSON>, update<PERSON><PERSON><PERSON>, delete<PERSON>lum<PERSON> } = useData()
  const [isAddO<PERSON>, setIsAddOpen] = useState(false)
  const [editing<PERSON><PERSON><PERSON>, setEditingAlumni] = useState<any>(null)
  const [isMediaBrowserOpen, setIsMediaBrowserOpen] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    graduationYear: "",
    degree: "",
    currentPosition: "",
    company: "",
    location: "",
    image: "",
    achievements: "",
  })

  const handleAdd = () => {
    if (formData.name && formData.graduationYear && formData.degree) {
      const achievementsArray = formData.achievements
        ? formData.achievements.split(',').map(a => a.trim()).filter(a => a)
        : []
      
      addAlumni({
        ...formData,
        achievements: achievementsArray,
      })
      
      setFormData({
        name: "",
        graduationYear: "",
        degree: "",
        currentPosition: "",
        company: "",
        location: "",
        image: "",
        achievements: "",
      })
      setIsAddOpen(false)
    }
  }

  const handleEdit = (alumni: any) => {
    setEditingAlumni(alumni)
    setFormData({
      name: alumni.name,
      graduationYear: alumni.graduationYear,
      degree: alumni.degree,
      currentPosition: alumni.currentPosition || "",
      company: alumni.company || "",
      location: alumni.location || "",
      image: alumni.image || "",
      achievements: alumni.achievements ? alumni.achievements.join(', ') : "",
    })
  }

  const handleUpdate = () => {
    if (editingAlumni && formData.name && formData.graduationYear && formData.degree) {
      const achievementsArray = formData.achievements
        ? formData.achievements.split(',').map(a => a.trim()).filter(a => a)
        : []
      
      updateAlumni(editingAlumni.id, {
        ...formData,
        achievements: achievementsArray,
      })
      
      setEditingAlumni(null)
      setFormData({
        name: "",
        graduationYear: "",
        degree: "",
        currentPosition: "",
        company: "",
        location: "",
        image: "",
        achievements: "",
      })
    }
  }

  const handleDelete = (id: number) => {
    deleteAlumni(id)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Alumni Management</h2>
          <p className="text-gray-600">Manage alumni profiles and achievements</p>
        </div>
        <Dialog open={isAddOpen} onOpenChange={setIsAddOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Alumni
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Alumni</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter full name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="graduationYear">Graduation Year</Label>
                <Input
                  id="graduationYear"
                  value={formData.graduationYear}
                  onChange={(e) => setFormData({ ...formData, graduationYear: e.target.value })}
                  placeholder="e.g., 2024"
                />
              </div>
              <div className="col-span-2 space-y-2">
                <Label htmlFor="degree">Degree</Label>
                <Input
                  id="degree"
                  value={formData.degree}
                  onChange={(e) => setFormData({ ...formData, degree: e.target.value })}
                  placeholder="e.g., PhD in Computer Science"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="currentPosition">Current Position</Label>
                <Input
                  id="currentPosition"
                  value={formData.currentPosition}
                  onChange={(e) => setFormData({ ...formData, currentPosition: e.target.value })}
                  placeholder="e.g., Senior Software Engineer"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="company">Company/Organization</Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                  placeholder="e.g., Google"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  placeholder="e.g., San Francisco, CA"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="image">Profile Image URL</Label>
                <div className="flex gap-2">
                  <Input
                    id="image"
                    value={formData.image}
                    onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                    placeholder="Enter image URL or browse media"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsMediaBrowserOpen(true)}
                  >
                    <ImageIcon className="h-4 w-4 mr-2" />
                    Browse
                  </Button>
                </div>
              </div>
              <div className="col-span-2 space-y-2">
                <Label htmlFor="achievements">Achievements (comma-separated)</Label>
                <Textarea
                  id="achievements"
                  value={formData.achievements}
                  onChange={(e) => setFormData({ ...formData, achievements: e.target.value })}
                  placeholder="e.g., Best Thesis Award, Published 10+ papers, Patent holder"
                  rows={3}
                />
              </div>
            </div>
            <div className="flex gap-3 mt-6">
              <Button onClick={handleAdd} className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Add Alumni
              </Button>
              <Button variant="outline" onClick={() => setIsAddOpen(false)}>
                Cancel
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Alumni Table */}
      <Card>
        <CardHeader>
          <CardTitle>Alumni List ({alumni.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Alumni</TableHead>
                <TableHead>Graduation</TableHead>
                <TableHead>Current Role</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {alumni.map((alumnus) => (
                <TableRow key={alumnus.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Image
                        src={alumnus.image || "/placeholder-user.jpg"}
                        alt={alumnus.name}
                        width={40}
                        height={40}
                        className="rounded-full object-cover"
                      />
                      <div>
                        <div className="font-medium">{alumnus.name}</div>
                        <div className="text-sm text-gray-500">{alumnus.degree}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">Class of {alumnus.graduationYear}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      {alumnus.currentPosition && (
                        <div className="text-sm">{alumnus.currentPosition}</div>
                      )}
                      {alumnus.company && (
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Building className="h-3 w-3" />
                          {alumnus.company}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {alumnus.location && (
                      <div className="flex items-center gap-1 text-sm">
                        <MapPin className="h-3 w-3 text-gray-400" />
                        {alumnus.location}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" onClick={() => handleEdit(alumnus)}>
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(alumnus.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={!!editingAlumni} onOpenChange={() => setEditingAlumni(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Alumni</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Full Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter full name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-graduationYear">Graduation Year</Label>
              <Input
                id="edit-graduationYear"
                value={formData.graduationYear}
                onChange={(e) => setFormData({ ...formData, graduationYear: e.target.value })}
                placeholder="e.g., 2024"
              />
            </div>
            <div className="col-span-2 space-y-2">
              <Label htmlFor="edit-degree">Degree</Label>
              <Input
                id="edit-degree"
                value={formData.degree}
                onChange={(e) => setFormData({ ...formData, degree: e.target.value })}
                placeholder="e.g., PhD in Computer Science"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-currentPosition">Current Position</Label>
              <Input
                id="edit-currentPosition"
                value={formData.currentPosition}
                onChange={(e) => setFormData({ ...formData, currentPosition: e.target.value })}
                placeholder="e.g., Senior Software Engineer"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-company">Company/Organization</Label>
              <Input
                id="edit-company"
                value={formData.company}
                onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                placeholder="e.g., Google"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-location">Location</Label>
              <Input
                id="edit-location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                placeholder="e.g., San Francisco, CA"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-image">Profile Image URL</Label>
              <div className="flex gap-2">
                <Input
                  id="edit-image"
                  value={formData.image}
                  onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                  placeholder="Enter image URL or browse media"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsMediaBrowserOpen(true)}
                >
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Browse
                </Button>
              </div>
            </div>
            <div className="col-span-2 space-y-2">
              <Label htmlFor="edit-achievements">Achievements (comma-separated)</Label>
              <Textarea
                id="edit-achievements"
                value={formData.achievements}
                onChange={(e) => setFormData({ ...formData, achievements: e.target.value })}
                placeholder="e.g., Best Thesis Award, Published 10+ papers, Patent holder"
                rows={3}
              />
            </div>
          </div>
          <div className="flex gap-3 mt-6">
            <Button onClick={handleUpdate} className="flex-1">
              <Save className="h-4 w-4 mr-2" />
              Update Alumni
            </Button>
            <Button variant="outline" onClick={() => setEditingAlumni(null)}>
              Cancel
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Media Browser */}
      <MediaBrowser
        isOpen={isMediaBrowserOpen}
        onClose={() => setIsMediaBrowserOpen(false)}
        onSelect={(url) => setFormData({ ...formData, image: url })}
        fileType="image"
        title="Select Profile Image"
      />
    </div>
  )
}
