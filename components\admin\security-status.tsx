"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, Eye, Lock, Globe, AlertTriangle, CheckCircle } from "lucide-react"

export default function SecurityStatus() {
  const isProduction = process.env.NODE_ENV === 'production'
  const hasAdminPassword = process.env.ADMIN_PASSWORD !== 'your-secure-password-here'

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Security Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Security Level */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Security Level:</span>
          <Badge variant={hasAdminPassword ? "default" : "destructive"}>
            {hasAdminPassword ? "Basic Protection" : "Needs Setup"}
          </Badge>
        </div>

        {/* Protected Areas */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <Lock className="h-4 w-4 text-green-600" />
            Protected (Password Required)
          </h4>
          <ul className="text-sm text-gray-600 space-y-1 ml-6">
            <li>• Admin panel (/admin)</li>
            <li>• Media upload API</li>
            <li>• Content management</li>
          </ul>
        </div>

        {/* Public Areas */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <Globe className="h-4 w-4 text-blue-600" />
            Public (Anyone Can Access)
          </h4>
          <ul className="text-sm text-gray-600 space-y-1 ml-6">
            <li>• Main website pages</li>
            <li>• Uploaded media files (/uploads/)</li>
            <li>• All images and documents</li>
          </ul>
        </div>

        {/* Warnings */}
        {!hasAdminPassword && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Security Warning:</strong> Please set a strong admin password in your environment variables.
            </AlertDescription>
          </Alert>
        )}

        <Alert>
          <Eye className="h-4 w-4" />
          <AlertDescription>
            <strong>Important:</strong> All uploaded media files are publicly accessible. 
            Anyone with the direct URL can view them.
          </AlertDescription>
        </Alert>

        {/* Recommendations */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Recommendations:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li className={hasAdminPassword ? "line-through text-gray-400" : ""}>
              {hasAdminPassword ? "✅" : "❌"} Set strong admin password
            </li>
            <li>⚠️ Review uploaded files for sensitive content</li>
            <li>💡 Consider protected file serving for sensitive documents</li>
            <li>🔄 Regular password updates</li>
          </ul>
        </div>

        {/* Status Summary */}
        <div className="pt-4 border-t">
          <div className="flex items-center gap-2">
            {hasAdminPassword ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-red-600" />
            )}
            <span className="text-sm font-medium">
              {hasAdminPassword 
                ? "Admin panel is secured" 
                : "Admin panel needs password setup"
              }
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
